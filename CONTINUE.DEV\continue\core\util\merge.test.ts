// File generated by Continue

import mergeJson from "./merge";

describe("mergeJson", () => {
  it("should merge two simple JSON objects", () => {
    const first = { a: 1, b: 2 };
    const second = { b: 3, c: 4 };
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: 1, b: 3, c: 4 });
  });

  it('should overwrite values when merge<PERSON>ehavior is "overwrite"', () => {
    const first = { a: 1, b: 2 };
    const second = { b: 3, c: 4 };
    const result = mergeJson(first, second, "overwrite");
    expect(result).toEqual({ a: 1, b: 3, c: 4 });
  });

  it("should merge nested objects", () => {
    const first = { a: { b: 1 } };
    const second = { a: { c: 2 } };
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: { b: 1, c: 2 } });
  });

  it("should merge arrays without mergeKeys", () => {
    const first = { a: [1, 2] };
    const second = { a: [3, 4] };
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: [1, 2, 3, 4] });
  });

  it("should merge arrays with mergeKeys", () => {
    const first = {
      a: [
        { id: 1, value: "first" },
        { id: 2, value: "second" },
      ],
    };
    const second = {
      a: [
        { id: 2, value: "updated" },
        { id: 3, value: "third" },
      ],
    };
    const mergeKeys = {
      a: (item1: any, item2: any) => item1.id === item2.id,
    };
    const result = mergeJson(first, second, undefined, mergeKeys);
    expect(result).toEqual({
      a: [
        { id: 1, value: "first" },
        { id: 2, value: "updated" },
        { id: 3, value: "third" },
      ],
    });
  });

  it("should handle non-object values correctly", () => {
    const first = { a: 1, b: "string", c: true };
    const second = { a: 2, b: "new string", c: false };
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: 2, b: "new string", c: false });
  });

  it("should handle null and undefined values correctly", () => {
    const first = { a: 1, b: null };
    const second = { b: 2, c: undefined };
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: 1, b: 2, c: undefined });
  });

  it("should handle empty objects", () => {
    const first = {};
    const second = { a: 1 };
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: 1 });
  });

  it("should handle empty second object", () => {
    const first = { a: 1 };
    const second = {};
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: 1 });
  });

  it("should handle errors gracefully", () => {
    const first = { a: 1 };
    const second = { b: () => {} }; // Functions are not serializable in JSON
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: 1, b: expect.any(Function) });
  });

  it("should merge deeply nested objects", () => {
    const first = { a: { b: { c: 1 } } };
    const second = { a: { b: { d: 2 } } };
    const result = mergeJson(first, second);
    expect(result).toEqual({ a: { b: { c: 1, d: 2 } } });
  });
});
