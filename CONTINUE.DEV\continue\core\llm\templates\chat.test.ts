// Generated by continue
import { ChatMessage } from "../../index";

import {
  anthropicTemplateMessages,
  chatmlTemplateMessages,
  codeLlama70bTemplateMessages,
  deepseekTemplateMessages,
  gemmaTemplateMessage,
  graniteTemplateMessages,
  llama2TemplateMessages,
  llama3TemplateMessages,
  llavaTemplateMessages,
  neuralChatTemplateMessages,
  openchatTemplateMessages,
  phi2TemplateMessages,
  phindTemplateMessages,
  templateAlpacaMessages,
  xWinCoderTemplateMessages,
  zephyrTemplateMessages,
} from "./chat.js";

describe.skip("Chat Templates", () => {
  describe("llama2TemplateMessages", () => {
    it("should generate the correct prompt with system message and user messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "Hello" },
        { role: "assistant", content: "Hi there!" },
        { role: "user", content: "How are you?" },
      ];
      const prompt = llama2TemplateMessages(messages);
      const expectedPrompt = `<s>[INST] <<SYS>>
   You are a helpful assistant.
  <</SYS>>
  
  Hello [/INST]Hi there!</s><s>[INST] How are you? [/INST]`;
      expect(prompt).toBe(expectedPrompt);
    });

    it("should generate the correct prompt without system message", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "Tell me a joke." },
      ];
      const prompt = llama2TemplateMessages(messages);
      const expectedPrompt = `[INST] Tell me a joke. [/INST]`;
      expect(prompt).toBe(expectedPrompt);
    });

    it("should skip initial assistant messages", () => {
      const messages: ChatMessage[] = [
        { role: "assistant", content: "Initial assistant message." },
        { role: "user", content: "What is the time?" },
      ];
      const prompt = llama2TemplateMessages(messages);
      const expectedPrompt = `[INST] What is the time? [/INST]`;
      expect(prompt).toBe(expectedPrompt);
    });

    it("should handle empty messages", () => {
      const messages: ChatMessage[] = [];
      const prompt = llama2TemplateMessages(messages);
      expect(prompt).toBe("");
    });

    it("should handle messages with empty system message", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "" },
        { role: "user", content: "Hello" },
      ];
      const prompt = llama2TemplateMessages(messages);
      const expectedPrompt = `[INST] Hello [/INST]`;
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("anthropicTemplateMessages", () => {
    it("should generate the correct prompt with user and assistant messages", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "Hello" },
        { role: "assistant", content: "Hi there!" },
        { role: "user", content: "How are you?" },
      ];
      const prompt = anthropicTemplateMessages(messages);
      const expectedPrompt =
        "\n\nHuman: Hello \n\nAssistant: Hi there! \n\nHuman: How are you? \n\nAssistant:";
      expect(prompt).toBe(expectedPrompt);
    });

    it("should start with 'Hello.' if first message is not from user or system", () => {
      const messages: ChatMessage[] = [
        { role: "assistant", content: "I am an assistant." },
        { role: "user", content: "Tell me a joke." },
      ];
      const prompt = anthropicTemplateMessages(messages);
      const expectedPrompt =
        "\n\nHuman: Hello.\n\nAssistant: I am an assistant. \n\nHuman: Tell me a joke. \n\nAssistant:";
      expect(prompt).toBe(expectedPrompt);
    });

    it("should handle empty messages", () => {
      const messages: ChatMessage[] = [];
      const prompt = anthropicTemplateMessages(messages);
      const expectedPrompt = "\n\nHuman: Hello.\n\nAssistant:";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("llavaTemplateMessages", () => {
    it("should generate the correct prompt with user messages", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "Describe this image." },
      ];
      const prompt = llavaTemplateMessages(messages);
      const expectedPrompt =
        "A chat between a curious user and an artificial intelligence assistant. The assistant gives helpful, detailed, and polite answers to the user's questions.USER: <image>Describe this image.ASSISTANT: ";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("zephyrTemplateMessages", () => {
    it("should generate the correct prompt with system and user messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System message." },
        { role: "user", content: "Hello" },
      ];
      const prompt = zephyrTemplateMessages(messages);
      const expectedPrompt =
        "<|system|>System message.</s>\n<|user|>\nHello</s>\n<|assistant|>\n";
      expect(prompt).toBe(expectedPrompt);
    });

    it("should handle empty system message", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "" },
        { role: "user", content: "Hello" },
      ];
      const prompt = zephyrTemplateMessages(messages);
      const expectedPrompt =
        "<|system|> </s>\n<|user|>\nHello</s>\n<|assistant|>\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("chatmlTemplateMessages", () => {
    it("should generate the correct prompt with system and user messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System prompt" },
        { role: "user", content: "Hello" },
      ];
      const prompt = chatmlTemplateMessages(messages);
      const expectedPrompt =
        "<|im_start|>system\nSystem prompt<|im_end|>\n<|im_start|>user\nHello<|im_end|>\n<|im_start|>assistant\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("templateAlpacaMessages", () => {
    it("should generate the correct prompt with user message", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "Explain recursion." },
      ];
      const prompt = templateAlpacaMessages(messages);
      const expectedPrompt =
        "Below is an instruction that describes a task. Write a response that appropriately completes the request.\n\n### Instruction:\nExplain recursion.\n\n### Response:\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("deepseekTemplateMessages", () => {
    it("should generate the correct prompt with user and assistant messages", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "What is polymorphism in OOP?" },
        { role: "assistant", content: "Explanation of polymorphism." },
      ];
      const prompt = deepseekTemplateMessages(messages);
      const expectedPrompt =
        "You are an AI programming assistant, utilizing the DeepSeek Coder model, developed by DeepSeek Company, and your  role is to assist with questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will not answer.\n### Instruction:\nWhat is polymorphism in OOP?\n### Response:\nExplanation of polymorphism.<|EOT|>\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("phi2TemplateMessages", () => {
    it("should generate the correct prompt with system and user messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System message." },
        { role: "user", content: "Tell me a story." },
      ];
      const prompt = phi2TemplateMessages(messages);
      const expectedPrompt =
        "\n\nInstruct: System message. \n\nInstruct: Tell me a story. \n\nOutput: ";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("phindTemplateMessages", () => {
    it("should generate the correct prompt with system and user messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System prompt" },
        { role: "user", content: "What's the weather?" },
      ];
      const prompt = phindTemplateMessages(messages);
      const expectedPrompt =
        "### System Prompt\nSystem prompt\n\n### User Message\nWhat's the weather?\n### Assistant\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("openchatTemplateMessages", () => {
    it("should generate the correct prompt with user and assistant messages", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "Hello" },
        { role: "assistant", content: "Hi!" },
        { role: "user", content: "How are you?" },
      ];
      const prompt = openchatTemplateMessages(messages);
      const expectedPrompt =
        "GPT4 Correct User: Hello<|end_of_turn|>GPT4 Correct Assistant: Hi!<|end_of_turn|>GPT4 Correct User: How are you?<|end_of_turn|>GPT4 Correct Assistant:";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("xWinCoderTemplateMessages", () => {
    it("should generate the correct prompt with system and user messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System prompt" },
        { role: "user", content: "Explain closures in JavaScript." },
      ];
      const prompt = xWinCoderTemplateMessages(messages);
      const expectedPrompt =
        "<system>: System prompt<user>: Explain closures in JavaScript.<AI>: ";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("codeLlama70bTemplateMessages", () => {
    it("should generate the correct prompt with multiple messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System prompt" },
        { role: "user", content: "First user query" },
        { role: "assistant", content: "Model response to first query" },
        { role: "user", content: "Second user query" },
      ];
      const prompt = codeLlama70bTemplateMessages(messages);
      const expectedPrompt =
        "<s>Source: system\n\n System prompt <step> Source: user\n\n First user query <step> Source: assistant\n\n Model response to first query <step> Source: user\n\n Second user query <step> Source: assistant\nDestination: user\n\n";
      expect(prompt).toBe(prompt); // Since the function returns prompt
    });
  });

  describe("llama3TemplateMessages", () => {
    it("should generate the correct prompt with messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System prompt" },
        { role: "user", content: "Tell me about AI." },
      ];
      const prompt = llama3TemplateMessages(messages);
      const expectedPrompt =
        "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\nSystem prompt<|eot_id|>\n<|start_header_id|>user<|end_header_id|>\nTell me about AI.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("gemmaTemplateMessage", () => {
    it("should generate the correct prompt with messages", () => {
      const messages: ChatMessage[] = [
        { role: "user", content: "How do I reverse a string in Python?" },
      ];
      const prompt = gemmaTemplateMessage(messages);
      const expectedPrompt =
        "<start_of_turn>user\nHow do I reverse a string in Python?<end_of_turn>\n<start_of_turn>model\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("graniteTemplateMessages", () => {
    it("should generate the correct prompt with system, user, and assistant messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "System prompt" },
        { role: "user", content: "What's the capital of France?" },
        { role: "assistant", content: "Paris is the capital of France." },
      ];
      const prompt = graniteTemplateMessages(messages);
      const expectedPrompt =
        "\n\nSystem:\n System prompt\n\nQuestion:\nWhat's the capital of France?\n\nAnswer:\nParis is the capital of France.\n\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });

  describe("neuralChatTemplateMessages", () => {
    it("should generate the correct prompt with system and user messages", () => {
      const messages: ChatMessage[] = [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "Tell me a joke." },
      ];
      const prompt = neuralChatTemplateMessages(messages);
      const expectedPrompt =
        "### System:\nYou are a helpful assistant.\n### User:\nTell me a joke.\n### Assistant:\n";
      expect(prompt).toBe(expectedPrompt);
    });
  });
});
