---
title: "Intro to Roles"
description: "Models in Continue can be configured to be used for various roles in the extension."
sidebarTitle: "Introduction"
icon: "book-open"
---

- [`chat`](/customize/model-roles/chat): Used for chat conversations in the extension sidebar
- [`autocomplete`](/customize/model-roles/autocomplete): Used for autocomplete code suggestions in the editor
- [`edit`](/customize/model-roles/edit): Used to generate code based on edit prompts
- [`apply`](/customize/model-roles/apply): Used to decide how to apply edits to a file
- [`embed`](/customize/model-roles/embeddings): Used to generate embeddings used for vector search (@Codebase and @Docs context providers)
- [`rerank`](/customize/model-roles/reranking): Used to rerank results from vector search

These roles can be specified for a `config.yaml` model block using `roles`. See the [YAML Specification](/reference#models) for more details.

## Selecting model roles

You can control which of the models in your assistant for a given role will be currently used for that role. Above the main input, click the 3 dots and then the cube icon to expand the `Models` section. Then you can use the dropdowns to select an active model for each role.

<Frame>
  <img src="/images/settings-model-roles-5e5f8a6bd9137b70cf94178a7e45847c.png" />
</Frame>

<Note>
  ### INFO

`roles` are not explicitly defined within `config.json` (deprecated) - they are infered by the top level keys like `embeddingsProvider`

</Note>
