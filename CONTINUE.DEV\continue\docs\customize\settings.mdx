---
title: "User Settings Page"
description: "Reference for Adjusting User-Specific Settings"
keywords: [config, settings, configuration, customize, customization, sidebar]
---

The **User Settings page** can be accessed by clicking the gear icon in the header of the Continue sidebar and then selecting the "Settings" tab.

![settings](/images/settings-header.png)

Which takes you to this page (select the "Settings" tab):

![User Settings Page](/images/settings-page.png)

Below that, the following settings which are not part of a configuration file are available:

- **Show Session Tabs**: If on, displays tabs above the chat as an alternative way to organize and access your sessions. Off by default
- **Wrap Codeblocks**: If on, enables text wrapping in code blocks. Off by default
- **Show Chat Scrollbar**: If on, enables a scrollbar in the chat window. Off by default
- **Text-to-Speech Output**: If on, reads LLM responses aloud with TTS. Off by default
- **Enable Session Titles**: If on, generates summary titles for each chat session after the first message, using the current Chat model. On by default
- **Format Markdown**: If off, shows responses as raw text. On by default
- **Allow Anonymous Telemetry**: If on, allows Continue to send anonymous telemetry. **On** by default
- **Enable Indexing**: Enables indexing of the codebase for the @codebase and @code context providers. **On** by default
- **Font Size**: Specifies base font size for UI elements
- **Multiline Autocompletions**: Controls multiline completions for autocomplete. Can be set to `always`, `never`, or `auto`. Defaults to `auto`
- **Autocomplete timeout**: Maximum time in milliseconds for autocomplete request/retrieval. Defaults to 150
- **Autocomplete debounce**: Minimum time in milliseconds to trigger an autocomplete request after a change. Defaults to 250
- **Disable autocomplete in files**: List of comma-separated glob pattern to disable autocomplete in matching files. E.g., `**/*.{txt,md}`

## Experimental settings

At the bottom of the User Settings page there is an "Experimental Settings" section which contains new or otherwise experimental settings, including:

<Warning>
  **Auto-Accept Agent Edits**: Be very careful with this setting. When turned
  on, Agent mode's edit tool can make changes to files with no manual review or
  guaranteed stopping point. If on, diffs generated by the edit tool are
  automatically accepted and Agent proceeds with the next conversational turn.
  Off by default.
</Warning>
