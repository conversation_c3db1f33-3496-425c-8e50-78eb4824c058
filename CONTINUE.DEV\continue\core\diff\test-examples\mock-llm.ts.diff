import { ChatMessage, CompletionOptions, ModelProvider } from "../../index.js";
import { BaseLLM } from "../index.js";

class Mock extends BaseLLM {
  static Completion = "Test Completion";
  static providerName: ModelProvider = "mock";

  protected async *_streamComplete(
    prompt: string,
    options: CompletionOptions,
  ): AsyncGenerator<string> {
    yield Mock.Completion;
  }

  protected async *_streamChat(
    messages: ChatMessage[],
    options: CompletionOptions,
  ): AsyncGenerator<ChatMessage> {
    yield {
      role: "assistant",
      content: Mock.Completion,
    };
  }
}

export default Mock;

---

import { ChatMessage, CompletionOptions, ModelProvider } from "../../index.js";
import { BaseLLM } from "../index.js";

class Mock extends BaseLLM {
  private static _completion: string = "Test Completion";
  static providerName: ModelProvider = "mock";

  static get completion(): string {
    return Mock._completion;
  }

  static set completion(value: string) {
    Mock._completion = value;
  }

  protected async *_streamComplete(
    prompt: string,
    options: CompletionOptions,
  ): AsyncGenerator<string> {
    yield Mock.completion;
  }

  protected async *_streamChat(
    messages: ChatMessage[],
    options: CompletionOptions,
  ): AsyncGenerator<ChatMessage> {
    yield {
      role: "assistant",
      content: Mock.completion,
    };
  }
}

export default Mock;

---

 import { ChatMessage, CompletionOptions, ModelProvider } from "../../index.js";
 import { BaseLLM } from "../index.js";
 
 class Mock extends BaseLLM {
-   static Completion = "Test Completion";
+   private static _completion: string = "Test Completion";
   static providerName: ModelProvider = "mock";
 
+   static get completion(): string {
+     return Mock._completion;
+   }
+ 
+   static set completion(value: string) {
+     Mock._completion = value;
+   }
+ 
   protected async *_streamComplete(
     prompt: string,
     options: CompletionOptions,
   ): AsyncGenerator<string> {
-     yield Mock.Completion;
+     yield Mock.completion;
   }
 
   protected async *_streamChat(
     messages: ChatMessage[],
     options: CompletionOptions,
   ): AsyncGenerator<ChatMessage> {
     yield {
       role: "assistant",
-       content: Mock.Completion,
+       content: Mock.completion,
     };
   }
 }
 
 export default Mock;