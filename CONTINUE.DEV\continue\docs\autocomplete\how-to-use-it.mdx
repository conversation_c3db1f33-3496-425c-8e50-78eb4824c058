---
title: "Autocomplete"
sidebarTitle: "How To Use It"
icon: "circle-question"
---

<Frame>
  <img src="/images/autocomplete-9d4e3f7658d3e65b8e8b20f2de939675.gif" />
</Frame>

## How to use it

Autocomplete provides inline code suggestions as you type. To enable it, simply click the "Continue" button in the status bar at the bottom right of your IDE or ensure the "Enable Tab Autocomplete" option is checked in your IDE settings.

### Accepting a full suggestion

Accept a full suggestion by pressing `Tab`

### Rejecting a full suggestion

Reject a full suggestion with `Esc`

### Partially accepting a suggestion

For more granular control, use `cmd/ctrl` + `→` to accept parts of the suggestion word-by-word.

### Forcing a suggestion (VS Code)

If you want to trigger a suggestion immediately without waiting, or if you've dismissed a suggestion and want a new one, you can force it by using the keyboard shortcut **`cmd/ctrl` + `alt` + `space`**.
