---
title: "Context Providers"
description: "Context Providers allow you to type '@' and see a dropdown of content that can all be fed to the LLM as context. Every context provider is a plugin, which means if you want to reference some source of information that you don't see here, you can request (or build!) a new context provider."
---

As an example, say you are working on solving a new GitHub Issue. You type '@Issue' and select the one you are working on. Continue can now see the issue title and contents. You also know that the issue is related to the files 'readme.md' and 'helloNested.py', so you type '@readme' and '@hello' to find and select them. Now these 3 "Context Items" are displayed inline with the rest of your input.

![Context Items](/images/customize/images/context-provider-example-0c96ff77286fa970b23dddfdc1fa986a.png)

## Context blocks

You can add context providers to assistants using [`context` blocks](/hub/blocks/block-types#context). Explore available context blocks in [the hub](https://hub.continue.dev/explore/context).

## Built-in Context Providers

You can add any built-in context-providers in your config file as shown below:

### `@File`

Reference any file in your current workspace.

- YAML
- JSON

config.yaml

```
context:  - provider: file
```

config.json

```json
{ "contextProviders": [{ "name": "file" }] }
```

### `@Code`

Reference specific functions or classes from throughout your project.

- YAML
- JSON

config.yaml

```
context:  - provider: code
```

config.json

```json
{ "contextProviders": [{ "name": "code" }] }
```

### `@Git Diff`

Reference all of the changes you've made to your current branch. This is useful if you want to summarize what you've done or ask for a general review of your work before committing.

- YAML
- JSON

config.yaml

```
context:  - provider: diff
```

config.json

```json
{ "contextProviders": [{ "name": "diff" }] }
```

### `@Current File`

Reference the currently open file.

- YAML
- JSON

config.yaml

```
context:  - provider: currentFile
```

config.json

```json
{ "contextProviders": [{ "name": "currentFile" }] }
```

### `@Terminal`

Reference the last command you ran in your IDE's terminal and its output.

- YAML
- JSON

config.yaml

```
context:  - provider: terminal
```

config.json

```json
{ "contextProviders": [{ "name": "terminal" }] }
```

### `@Docs`

Reference the contents from any documentation site.

- YAML
- JSON

config.yaml

```
context:  - provider: docs
```

config.json

```json
{ "contextProviders": [{ "name": "docs" }] }
```

Note that this will only enable the `@Docs` context provider.

To use it, you need to add a documentation site to your config file. See the [docs](/customization/overview#documentation-context) page for more information.

### `@Open`

Reference the contents of all of your open files. Set `onlyPinned` to `true` to only reference pinned files.

- YAML
- JSON

config.yaml

```
context:  - provider: open    params:      onlyPinned: true
```

config.json

```json
{ "contextProviders": [{ "name": "open", "params": { "onlyPinned": true } }] }
```

### `@Web`

Reference relevant pages from across the web, automatically determined from your input.

Optionally, set `n` to limit the number of results returned (default 6).

- YAML
- JSON

config.yaml

```
context:  - provider: web    params:      n: 5
```

config.json

```json
{ "contextProviders": [{ "name": "web", "params": { "n": 5 } }] }
```

### `@Codebase`

Reference the most relevant snippets from your codebase.

- YAML
- JSON

config.yaml

```
context:  - provider: codebase
```

config.json

```json
{ "contextProviders": [{ "name": "codebase" }] }
```

Read more about indexing and retrieval [here](/customization/overview#codebase-context).

### `@Folder`

Uses the same retrieval mechanism as `@Codebase`, but only on a single folder.

- YAML
- JSON

config.yaml

```
context:  - provider: folder
```

config.json

```json
{ "contextProviders": [{ "name": "folder" }] }
```

### `@Search`

Reference the results of codebase search, just like the results you would get from VS Code search.

- YAML
- JSON

config.yaml

```
context:  - provider: search    params:        maxResults: 100 # optional, defaults to 200
```

config.json

```json
{  "contextProviders": [    {      "name": "search",      "params": {        "maxResults": 100 // optional, defaults to 200      }    }  ]}
```

This context provider is powered by [ripgrep](https://github.com/BurntSushi/ripgrep).

### `@Url`

Reference the markdown converted contents of a given URL.

- YAML
- JSON

config.yaml

```
context:  - provider: url
```

config.json

```json
{ "contextProviders": [{ "name": "url" }] }
```

### `@Clipboard`

Reference recent clipboard items

- YAML
- JSON

config.yaml

```
context:  - provider: clipboard
```

config.json

```json
{ "contextProviders": [{ "name": "clipboard" }] }
```

### `@Tree`

Reference the structure of your current workspace.

- YAML
- JSON

config.yaml

```
context:  - provider: tree
```

config.json

```json
{ "contextProviders": [{ "name": "tree" }] }
```

### `@Problems`

Get Problems from the current file.

- YAML
- JSON

config.yaml

```
context:  - provider: problems
```

config.json

```json
{ "contextProviders": [{ "name": "problems" }] }
```

### `@Debugger`

Reference the contents of the local variables in the debugger. Currently only available in VS Code.

- YAML
- JSON

config.yaml

```
context:  - provider: debugger    params:      stackDepth: 3
```

config.json

```json
{ "contextProviders": [{ "name": "debugger", "params": { "stackDepth": 3 } }] }
```

Uses the top _n_ levels (defaulting to 3) of the call stack for that thread.

### `@Repository Map`

Reference the outline of your codebase. By default, signatures are included along with file in the repo map.

`includeSignatures` params can be set to false to exclude signatures. This could be necessary for large codebases and/or to reduce context size significantly. Signatures will not be included if indexing is disabled.

- YAML
- JSON

config.yaml

```
context:  - provider: repo-map    params:      includeSignatures: false # default true
```

config.json

```json
{  "contextProviders": [    {      "name": "repo-map",      "params": {        "includeSignatures": false // default true      }    }  ]}
```

Provides a list of files and the call signatures of top-level classes, functions, and methods in those files. This helps the model better understand how a particular piece of code relates to the rest of the codebase.

In the submenu that appears, you can select either `Entire codebase`, or specify a subfolder to generate the repostiory map from.

This context provider is inpsired by [Aider's repository map](https://aider.chat/2023/10/22/repomap.html).

### `@Operating System`

Reference the architecture and platform of your current operating system.

- YAML
- JSON

config.yaml

```
context:  - provider: os
```

config.json

```json
{ "contextProviders": [{ "name": "os" }] }
```

### Model Context Protocol

The [Model Context Protocol](https://modelcontextprotocol.io/introduction) is a standard proposed by Anthropic to unify prompts, context, and tool use. Continue supports any MCP server with the MCP context provider. Read their [quickstart](https://modelcontextprotocol.io/quickstart) to learn how to set up a local server and then set up your configuration like this:

- YAML
- JSON

config.yaml

```
mcpServers:  - name: My MCP Server    command: uvx    args:      - mcp-server-sqlite      - --db-path      - /Users/<USER>/test.db
```

config.json

```json
{
  "experimental": {
    "modelContextProtocolServers": [
      {
        "transport": {
          "type": "stdio",
          "command": "uvx",
          "args": ["mcp-server-sqlite", "--db-path", "/Users/<USER>/test.db"]
        }
      }
    ]
  }
}
```

You'll then be able to type "@" and see "MCP" in the context providers dropdown.

### `@Issue`

Reference the conversation in a GitHub issue.

- YAML
- JSON

config.yaml

```
context:  - provider: issue    params:      repos:        - owner: continuedev          repo: continue      githubToken: ghp_xxx
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "issue",
      "params": {
        "repos": [{ "owner": "continuedev", "repo": "continue" }],
        "githubToken": "ghp_xxx"
      }
    }
  ]
}
```

Make sure to include your own [GitHub personal access token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-fine-grained-personal-access-token) to avoid being rate-limited.

### `@Database`

Reference table schemas from Sqlite, Postgres, MSSQL, and MySQL databases.

- YAML
- JSON

config.yaml

```
context:  - provider: database    params:      connections:        - name: examplePostgres          connection_type: postgres          connection:            user: username            host: localhost            database: exampleDB            password: yourPassword            port: 5432        - name: exampleMssql           connection_type: mssql          connection:            user: username            server: localhost            database: exampleDB            password: yourPassword        - name: exampleSqlite          connection_type: sqlite          connection:            filename: /path/to/your/sqlite/database.db
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "database",
      "params": {
        "connections": [
          {
            "name": "examplePostgres",
            "connection_type": "postgres",
            "connection": {
              "user": "username",
              "host": "localhost",
              "database": "exampleDB",
              "password": "yourPassword",
              "port": 5432
            }
          },
          {
            "name": "exampleMssql",
            "connection_type": "mssql",
            "connection": {
              "user": "username",
              "server": "localhost",
              "database": "exampleDB",
              "password": "yourPassword"
            }
          },
          {
            "name": "exampleSqlite",
            "connection_type": "sqlite",
            "connection": { "filename": "/path/to/your/sqlite/database.db" }
          }
        ]
      }
    }
  ]
}
```

Each connection should include a unique name, the `connection_type`, and the necessary connection parameters specific to each database type.

Available connection types:

- `postgres`
- `mysql`
- `sqlite`

### `@Postgres`

Reference the schema of a table, and some sample rows

- YAML
- JSON

config.yaml

```
context:  - provider: postgres    params:      host: localhost      port: 5436      user: myuser      password: catsarecool      database: animals      schema: public      sampleRows: 3
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "postgres",
      "params": {
        "host": "localhost",
        "port": 5436,
        "user": "myuser",
        "password": "catsarecool",
        "database": "animals",
        "schema": "public",
        "sampleRows": 3
      }
    }
  ]
}
```

The only required settings are those for creating the database connection: `host`, `port`, `user`, `password`, and `database`.

By default, the `schema` filter is set to `public`, and the `sampleRows` is set to 3. You may unset the schema if you want to include tables from all schemas.

[Here is a short demo.](https://github.com/continuedev/continue/pull/859)

### `@Google`

Reference the results of a Google search.

- YAML
- JSON

config.yaml

```
context:  - provider: google    params:      serperApiKey: <YOUR_SERPER.DEV_API_KEY>
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "google",
      "params": { "serperApiKey": "<YOUR_SERPER.DEV_API_KEY>" }
    }
  ]
}
```

For example, type "@Google python tutorial" if you want to search and discuss ways of learning Python.

Note: You can get an API key for free at [serper.dev](https://serper.dev).

### `@Gitlab Merge Request`

Reference an open MR for this branch on GitLab.

- YAML
- JSON

config.yaml

```
context:  - provider: gitlab-mr    params:      token: "..."
```

config.json

```json
{ "contextProviders": [{ "name": "gitlab-mr", "params": { "token": "..." } }] }
```

You will need to create a [personal access token](https://docs.gitlab.com/ee/user/profile/personal_access_tokens.html) with the `read_api` scope.

#### Using Self-Hosted GitLab

You can specify the domain to communicate with by setting the `domain` parameter in your configurtion. By default this is set to `gitlab.com`.

- YAML
- JSON

config.yaml

```
context:  - provider: gitlab-mr    params:      token: "..."      domain: "gitlab.example.com"
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "gitlab-mr",
      "params": { "token": "...", "domain": "gitlab.example.com" }
    }
  ]
}
```

#### Filtering Comments

If you select some code to be edited, you can have the context provider filter out comments for other files. To enable this feature, set `filterComments` to `true`.

### `@Jira`

Reference the conversation in a Jira issue.

- YAML
- JSON

config.yaml

```
context:  - provider: jira    params:      domain: company.atlassian.net      token: ATATT...
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "jira",
      "params": { "domain": "company.atlassian.net", "token": "ATATT..." }
    }
  ]
}
```

Make sure to include your own [Atlassian API Token](https://id.atlassian.com/manage-profile/security/api-tokens), or use your `email` and `token`, with token set to your password for basic authentication. If you use your own Atlassian API Token, don't configure your email.

#### Jira Datacenter Support

This context provider supports both Jira API version 2 and 3. It will use version 3 by default since that's what the cloud version uses, but if you have the datacenter version of Jira, you'll need to set the API Version to 2 using the `apiVersion` property.

- YAML
- JSON

config.yaml

```
context:  - provider: jira    params:      apiVersion: "2"
```

config.json

```json
{ "contextProviders": [{ "name": "jira", "params": { "apiVersion": "2" } }] }
```

#### Issue Query

By default, the following query will be used to find issues:

```
assignee = currentUser() AND resolution = Unresolved order by updated DESC
```

You can override this query by setting the `issueQuery` parameter.

#### Max results

You can set the `maxResults` parameter to limit the number of results returned. The default is `50`.

### `@Discord`

Reference the messages in a Discord channel.

- YAML
- JSON

config.yaml

```
context:  - provider: discord    params:      discordKey: "bot token"      guildId: "**********"       channels:        - id: "123456"          name: "example-channel"        - id: "678901"           name: "example-channel-2"
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "discord",
      "params": {
        "discordKey": "bot token",
        "guildId": "**********",
        "channels": [
          { "id": "123456", "name": "example-channel" },
          { "id": "678901", "name": "example-channel-2" }
        ]
      }
    }
  ]
}
```

Make sure to include your own [Bot Token](https://discord.com/developers/applications), and join it to your related server . If you want more granular control over which channels are searched, you can specify a list of channel IDs to search in. If you don't want to specify any channels, just include the guild id(Server ID) and all channels will be included. The provider only reads text channels.

### `@HTTP`

The HttpContextProvider makes a POST request to the url passed in the configuration. The server must return 200 OK with a ContextItem object or an array of ContextItems.

- YAML
- JSON

config.yaml

```
context:  - provider: http    params:      url: "https://api.example.com/v1/users"
```

config.json

```json
{
  "contextProviders": [
    { "name": "http", "params": { "url": "https://api.example.com/v1/users" } }
  ]
}
```

The receiving URL should expect to receive the following parameters:

POST parameters

```json
{  query: string,  fullInput: string}
```

The response 200 OK should be a JSON object with the following structure:

Response

```
[  {    "name": "",    "description": "",    "content": ""  }]// OR{  "name": "",  "description": "",  "content": ""}
```

### `@Commits`

Reference specific git commit metadata and diff or all of the recent commits.

- YAML
- JSON

config.yaml

```
context:  - provider: commit    params:      Depth: 50      LastXCommitsDepth: 10
```

config.json

```json
{
  "contextProviders": [
    { "name": "commit", "params": { "Depth": 50, "LastXCommitsDepth": 10 } }
  ]
}
```

The depth is how many commits will be loaded into the submenu, defaults to 50. The LastXCommitsDepth is how many recent commits will be included, defaults to 10.

### `@Greptile`

Query a [Greptile](https://www.greptile.com/) index of the current repo/branch.

- YAML
- JSON

config.yaml

```
context:  - provider: greptile     params:      greptileToken: "..."      githubToken: "..."
```

config.json

```json
{
  "contextProviders": [
    {
      "name": "greptile",
      "params": { "GreptileToken": "...", "GithubToken": "..." }
    }
  ]
}
```

### Requesting Context Providers

Not seeing what you want? Create an issue [here](https://github.com/continuedev/continue/issues/new?assignees=TyDunn&labels=enhancement&projects=&template=feature-request-%F0%9F%92%AA.md&title=) to request a new Context Provider.
