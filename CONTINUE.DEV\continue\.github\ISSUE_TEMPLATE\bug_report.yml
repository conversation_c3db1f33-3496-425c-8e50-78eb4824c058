---
name: 🐛 Bug report
description: Create a report to help us fix your bug
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report this bug! This will help us find the cause of the problem quickly with less back-and-forth required.
  - type: checkboxes
    attributes:
      label: Before submitting your bug report
      options:
        - label: I believe this is a bug. I'll try to join the [Continue Discord](https://discord.gg/NWtdYexhMs) for questions
          required: false
        - label: I'm not able to find an [open issue](https://github.com/continuedev/continue/issues?q=is%3Aopen+is%3Aissue) that reports the same bug
          required: false
        - label: I've seen the [troubleshooting guide](https://docs.continue.dev/troubleshooting) on the Continue Docs
          required: false
  - type: textarea
    attributes:
      label: Relevant environment info
      description: |
        Feel free to omit any info that is not relevant to your issue.

        - **OS**: macOS
        - **Continue version**: v0.9.4
        - **IDE version**:  VSCode 1.85.1
        - Model: <PERSON> 3.5
        - Assistant configuration
      value: |
        - OS:
        - Continue version:
        - IDE version:
        - Model:
        - config:
          ```yaml

          ```
          OR link to assistant in Continue hub:
      render: Markdown
    validations:
      required: false
  - type: textarea
    attributes:
      label: Description
      description: |
        Please provide a clear and concise description of the bug
      placeholder: |
        Short description
    validations:
      required: false
  - type: textarea
    attributes:
      label: To reproduce
      description: Steps to reproduce the problem. If possible, include a gif, screenshot, or video to better illustrate.
      placeholder: |
        1. Go to …
        2. Click on …
        3. Scroll down to …
        4. See error
    validations:
      required: false
  - type: textarea
    id: logs
    attributes:
      label: Log output
      description: |
        Please refer to the [troubleshooting guide](https://docs.continue.dev/troubleshooting) in the Continue Docs for instructions on obtaining the logs. Copy either the relevant lines or the last 100 lines or so.
      render: Shell
