name: Docs Check

on:
  push:
    paths:
      - 'docs2/**'
  pull_request:
    paths:
      - 'docs2/**'

jobs:
  check-broken-links:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: docs2/package-lock.json

      - name: Install dependencies
        working-directory: ./docs2
        run: npm ci

      - name: Check for broken links
        working-directory: ./docs2
        run: npx mintlify broken-links