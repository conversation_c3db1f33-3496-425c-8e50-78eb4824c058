---
title: "Quick Start"
---

Cha<PERSON> makes it easy to ask for help from an AI without leaving your IDE. Get explanations, generate code, and iterate on solutions conversationally.

## Basic Usage

### 1. Start a Conversation

Type your question or request in the chat input and press Enter.

**Examples:**

- "Explain this function"
- "How do I handle errors in this code?"
- "Generate a test for this component"

### 2. Include Code Context

Select code in your editor, then use the keyboard shortcut to include it in your chat:

- VS Code
- JetBrains

Press `Cmd/Ctrl + L` to send selected code to chat

Press `Cmd/Ctrl + J` to send selected code to chat

### 3. Use @ for Additional Context

Type `@` to include specific context:

- `@Codebase` - Search your project for relevant code
- `@Docs` - Include documentation
- `@Files` - Reference specific files
- `@Terminal` - Include terminal output

## Working with Responses

When the AI provides code in its response, you'll see action buttons:

- **Apply to current file** - Replace your selected code
- **Insert at cursor** - Add code at your cursor position
- **Copy** - Copy code to clipboard

## Pro Tips

### Start Fresh

Press `Cmd/Ctrl + L` (VS Code) or `Cmd/Ctrl + J` (JetBrains) in an empty chat to start a new session.

### Be Specific

Include details about:

- What you're trying to accomplish
- Any constraints or requirements
- Your preferred coding style or patterns

### Iterate

If the first response isn't perfect:

- Ask follow-up questions
- Request modifications
- Provide additional context

## Common Use Cases

### Code Explanation

Select confusing code and ask "What does this code do?"

### Bug Fixing

Include error messages and ask "How do I fix this error?"

### Code Generation

Describe what you want: "Create a React component that displays a user profile"

### Refactoring

Select code and ask "How can I make this more efficient?"

---

_Chat is designed for quick interactions and iterative problem-solving. Don't hesitate to ask follow-up questions!_
