// @ts-nocheck

const getAddress = (person: Person): Address => {
  // TODO
};

const logPerson = (person: Person) => {
  // TODO
};

const getHardcodedAddress = (): Address => {
  // TODO
};

const getAddresses = (people: Person[]): Address[] => {
  // TODO
};

const logPersonWithAddres = (person: Person<Address>): Person<Address> => {
  // TODO
};

const logPersonOrAddress = (person: Person | Address): Person | Address => {
  // TODO
};

const logPersonAndAddress = (person: Person, address: Address) => {
  // TODO
};
