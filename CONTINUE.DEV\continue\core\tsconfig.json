{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext", "ES2021"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noEmitOnError": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["jest", "node"]}, "include": ["./**/*.ts", "./**/*.js", "./**/*.d.ts"], "exclude": ["dist"]}